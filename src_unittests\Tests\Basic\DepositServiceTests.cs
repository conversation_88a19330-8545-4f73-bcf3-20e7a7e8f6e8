using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services;

namespace RazeWinComTr.Tests.Basic
{
    /// <summary>
    /// Tests for the DepositService class.
    /// These tests verify the behavior of deposit creation and approval,
    /// especially regarding balance transactions.
    /// </summary>
    public class DepositServiceTests : TestBase
    {
        /// <summary>
        /// Tests that when a deposit is created with pending status,
        /// no balance transaction is created.
        /// </summary>
        [Fact]
        public async Task CreateWithBalanceUpdateAsync_WithPendingStatus_DoesNotCreateBalanceTransaction()
        {
            // Arrange
            var dbContext = CreateDbContext("CreateWithBalanceUpdateAsync_WithPendingStatus_DoesNotCreateBalanceTransaction");
            var mockLocalizer = new Mock<IStringLocalizer<SharedResource>>();
            var mockWalletService = CreateMockWalletService();
            var mockLogger = CreateMockLogger<DepositService>();

            // Setup localizer to return the input string
            mockLocalizer.Setup(l => l[It.IsAny<string>()])
                .Returns<string>(s => new LocalizedString(s, s));
            mockLocalizer.Setup(l => l[It.IsAny<string>(), It.IsAny<object[]>()])
                .Returns<string, object[]>((s, args) => new LocalizedString(s, string.Format(s, args)));

            // Create a user
            var user = TestDataGenerator.CreateUser(1);
            dbContext.Users.Add(user);
            await dbContext.SaveChangesAsync();

            // Create balance transaction service
            var balanceTransactionService = new BalanceTransactionService(dbContext, mockLocalizer.Object);
            var tokenPriceService = CreateMockTokenPriceService(1.0m, 1); // Mock RZW price

            // Create deposit service
            var depositService = new DepositService(
                mockLocalizer.Object,
                dbContext,
                mockWalletService.Object,
                balanceTransactionService,
                tokenPriceService.Object,
                mockLogger.Object);

            // Create a deposit with pending status
            var deposit = new Deposit
            {
                UserId = user.UserId,
                DepositType = "Test",
                Amount = 100m,
                FullName = "Test User",
                ExtraData = "Test data",
                IpAddress = "127.0.0.1",
                ProcessStatus = "Pending",
                Status = DepositStatus.Pending,
                CreatedDate = DateTime.UtcNow
            };

            // Act
            await depositService.CreateWithBalanceUpdateAsync(deposit);

            // Assert
            var balanceTransactions = await dbContext.BalanceTransactions.ToListAsync();
            Assert.Empty(balanceTransactions); // No balance transaction should be created

            // Verify user balance hasn't changed
            var updatedUser = await dbContext.Users.FindAsync(user.UserId);
            Assert.Equal(0m, updatedUser!.Balance); // Balance should remain unchanged
        }

        /// <summary>
        /// Tests that when a deposit is created with approved status,
        /// a balance transaction is created and the user's balance is updated.
        /// </summary>
        [Fact]
        public async Task CreateWithBalanceUpdateAsync_WithApprovedStatus_CreatesBalanceTransaction()
        {
            // Arrange
            var dbContext = CreateDbContext("CreateWithBalanceUpdateAsync_WithApprovedStatus_CreatesBalanceTransaction");
            var mockLocalizer = new Mock<IStringLocalizer<SharedResource>>();
            var mockWalletService = CreateMockWalletService();
            var mockLogger = CreateMockLogger<DepositService>();

            // Setup localizer to return the input string
            mockLocalizer.Setup(l => l[It.IsAny<string>()])
                .Returns<string>(s => new LocalizedString(s, s));
            mockLocalizer.Setup(l => l[It.IsAny<string>(), It.IsAny<object[]>()])
                .Returns<string, object[]>((s, args) => new LocalizedString(s, string.Format(s, args)));

            // Create a user
            var user = TestDataGenerator.CreateUser(1);
            dbContext.Users.Add(user);
            await dbContext.SaveChangesAsync();

            // Create balance transaction service
            var balanceTransactionService = new BalanceTransactionService(dbContext, mockLocalizer.Object);
            var tokenPriceService = CreateMockTokenPriceService(1.0m, 1); // Mock RZW price

            // Create deposit service
            var depositService = new DepositService(
                mockLocalizer.Object,
                dbContext,
                mockWalletService.Object,
                balanceTransactionService,
                tokenPriceService.Object,
                mockLogger.Object);

            // Create a deposit with approved status
            var deposit = new Deposit
            {
                UserId = user.UserId,
                DepositType = "Test",
                Amount = 100m,
                FullName = "Test User",
                ExtraData = "Test data",
                IpAddress = "127.0.0.1",
                ProcessStatus = "Completed",
                Status = DepositStatus.Approved,
                CreatedDate = DateTime.UtcNow
            };

            // Act
            await depositService.CreateWithBalanceUpdateAsync(deposit);

            // Assert
            var balanceTransactions = await dbContext.BalanceTransactions.ToListAsync();
            Assert.Single(balanceTransactions); // One balance transaction should be created

            var transaction = balanceTransactions.First();
            Assert.Equal(user.UserId, transaction.UserId);
            Assert.Equal(TransactionType.Deposit, transaction.TransactionType);
            Assert.Equal(100m, transaction.Amount);
            Assert.Equal(0m, transaction.PreviousBalance);
            Assert.Equal(100m, transaction.NewBalance);
            Assert.Equal(deposit.Id, transaction.ReferenceId);
            Assert.Equal("Deposit", transaction.ReferenceType);

            // Verify user balance has been updated
            var updatedUser = await dbContext.Users.FindAsync(user.UserId);
            Assert.Equal(100m, updatedUser!.Balance); // Balance should be updated
        }

        /// <summary>
        /// Tests that when a deposit status is changed from pending to approved,
        /// a balance transaction is created and the user's balance is updated.
        /// </summary>
        [Fact]
        public async Task UpdatePaymentStatusAsync_FromPendingToApproved_CreatesBalanceTransaction()
        {
            // Arrange
            var dbContext = CreateDbContext("UpdatePaymentStatusAsync_FromPendingToApproved_CreatesBalanceTransaction");
            var mockLocalizer = new Mock<IStringLocalizer<SharedResource>>();
            var mockWalletService = CreateMockWalletService();
            var mockLogger = CreateMockLogger<DepositService>();

            // Setup localizer to return the input string
            mockLocalizer.Setup(l => l[It.IsAny<string>()])
                .Returns<string>(s => new LocalizedString(s, s));
            mockLocalizer.Setup(l => l[It.IsAny<string>(), It.IsAny<object[]>()])
                .Returns<string, object[]>((s, args) => new LocalizedString(s, string.Format(s, args)));

            // Create a user
            var user = TestDataGenerator.CreateUser(1);
            dbContext.Users.Add(user);
            await dbContext.SaveChangesAsync();

            // Create balance transaction service
            var balanceTransactionService = new BalanceTransactionService(dbContext, mockLocalizer.Object);
            var tokenPriceService = CreateMockTokenPriceService(1.0m, 1); // Mock RZW price

            // Create deposit service
            var depositService = new DepositService(
                mockLocalizer.Object,
                dbContext,
                mockWalletService.Object,
                balanceTransactionService,
                tokenPriceService.Object,
                mockLogger.Object);

            // Create a deposit with pending status
            var deposit = new Deposit
            {
                UserId = user.UserId,
                DepositType = "Test",
                Amount = 100m,
                FullName = "Test User",
                ExtraData = "Test data",
                IpAddress = "127.0.0.1",
                ProcessStatus = "Pending",
                Status = DepositStatus.Pending,
                CreatedDate = DateTime.UtcNow
            };

            dbContext.Deposits.Add(deposit);
            await dbContext.SaveChangesAsync();

            // Act - First verify no balance transaction exists
            var initialBalanceTransactions = await dbContext.BalanceTransactions.ToListAsync();
            Assert.Empty(initialBalanceTransactions); // No balance transaction should exist yet

            // Now update the status to approved
            var success = await depositService.UpdateDepositStatusAsync(deposit.Id, DepositStatus.Approved);

            // Assert
            Assert.True(success);

            var balanceTransactions = await dbContext.BalanceTransactions.ToListAsync();
            Assert.Single(balanceTransactions); // One balance transaction should be created

            var transaction = balanceTransactions.First();
            Assert.Equal(user.UserId, transaction.UserId);
            Assert.Equal(TransactionType.Deposit, transaction.TransactionType);
            Assert.Equal(100m, transaction.Amount);
            Assert.Equal(0m, transaction.PreviousBalance);
            Assert.Equal(100m, transaction.NewBalance);
            Assert.Equal(deposit.Id, transaction.ReferenceId);
            Assert.Equal("Deposit", transaction.ReferenceType);

            // Verify user balance has been updated
            var updatedUser = await dbContext.Users.FindAsync(user.UserId);
            Assert.Equal(100m, updatedUser!.Balance); // Balance should be updated

            // Verify deposit status has been updated
            var updatedDeposit = await dbContext.Deposits.FindAsync(deposit.Id);
            Assert.Equal(DepositStatus.Approved, updatedDeposit!.Status);
            Assert.Equal("Completed", updatedDeposit.ProcessStatus);
        }
    }
}
