using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.Wallet;

namespace RazeWinComTr.Tests.Mocks;

/// <summary>
/// Mock implementation of IWalletService that throws an exception on AddToWalletAsync
/// </summary>
public class MockWalletServiceWithFailure : IWalletService
{
    public Task<Wallet?> GetByIdAsync(int id)
    {
        return Task.FromResult<Wallet?>(null);
    }

    public Task<List<Wallet>> GetByUserIdAsync(int userId)
    {
        return Task.FromResult(new List<Wallet>());
    }

    public Task<List<Wallet>> GetTopNByUserIdAsync(int userId, int topN)
    {
        return Task.FromResult(new List<Wallet>());
    }

    public Task<Wallet?> GetByUserIdAndCoinIdAsync(int userId, int coinId)
    {
        return Task.FromResult<Wallet?>(null);
    }

    public Task<List<WalletViewModel>> GetListAsync()
    {
        return Task.FromResult(new List<WalletViewModel>());
    }

    //public Task<decimal> GetUserBalanceAsync(int userId, int coinId)
    //{
    //    return Task.FromResult(0m);
    //}

    //public Task<Wallet> CreateAsync(Wallet wallet)
    //{
    //    return Task.FromResult(wallet);
    //}

    public Task UpdateAsync(Wallet wallet)
    {
        return Task.CompletedTask;
    }

    public Task DeleteAsync(int id)
    {
        return Task.CompletedTask;
    }

    //public Task<Wallet> AddBalanceAsync(int userId, int coinId, decimal amount)
    //{
    //    throw new Exception("Wallet service failure");
    //}

    public Task<bool> DeductAvailableBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, AppDbContext? existingContext = null)
    {
        throw new Exception("Wallet service failure");
    }

    //public Task<Wallet> AddToWalletAsync(int userId, int coinId, decimal amount)
    //{
    //    throw new Exception("Wallet service failure");
    //}

    // NEW METHODS - Available Balance (Phase 2) - All throw exceptions
    public Task<decimal> GetUserAvailableBalanceAsync(int userId, int coinId)
    {
        throw new Exception("Wallet service failure");
    }

    public Task<Wallet?> GetByUserIdAndCoinIdAsync(int userId, int coinId, AppDbContext? existingContext = null)
    {
        throw new Exception("Wallet service failure");
    }

    public Task<Wallet> CreateAsync(Wallet wallet, AppDbContext? existingContext = null)
    {
        throw new Exception("Wallet service failure");
    }

    public Task UpdateAsync(Wallet wallet, AppDbContext? existingContext = null)
    {
        throw new Exception("Wallet service failure");
    }

    public Task<Wallet> AddAvailableBalanceAsync(int userId, RzwTokenInfo rzwTokenInfo, decimal amount, TradeType tradeType, AppDbContext? existingContext = null)
    {
        throw new Exception("Wallet service failure");
    }        
}
