# Mock Migration to Moq Framework

Bu dosya, test projemizdeki mock sınıflarının Moq framework'üne geçiş sürecini belgelemektedir.

## Tamamlanan Geçişler

### MockTokenPriceService → Moq Framework

**Durum**: ✅ Tamamlandı

**Değişiklikler**:
- `MockTokenPriceService` sınıfı obsolete olarak işaretlendi
- `TestBase.CreateMockTokenPriceService()` metodu Moq kullanacak şekilde güncellendi
- Tüm test dosyalarında `mockTokenPriceService.Object` kullanımına geçildi
- Geriye dönük uyumluluk için `CreateLegacyMockTokenPriceService()` metodu eklendi

**Yeni Kullanım**:
```csharp
// Eski kullanım (obsolete)
var mockTokenPriceService = new MockTokenPriceService(1.0m, 1);

// Yeni kullanım
var mockTokenPriceService = CreateMockTokenPriceService(1.0m, 1);
var service = new SomeService(mockTokenPriceService.Object);
```

**Avantajları**:
- Daha esnek mock konfigürasyonu
- Setup metotları ile davranış belirleme
- Verify metotları ile çağrı doğrulama
- Moq'un güçlü özelliklerinden yararlanma

### MockTradeService → Moq Framework

**Durum**: ✅ Tamamlandı

**Değişiklikler**:
- `ITradeService` interface'i oluşturuldu
- `TradeService` sınıfı `ITradeService` interface'ini implement ediyor
- `MockTradeService` sınıfı obsolete olarak işaretlendi
- `TestBase.CreateMockTradeService()` metodu Moq kullanacak şekilde güncellendi
- Geriye dönük uyumluluk için `CreateLegacyMockTradeService()` metotları eklendi
- Program.cs'de TradeService interface ile register edildi

**Yeni Kullanım**:
```csharp
// Eski kullanım (obsolete)
var mockTradeService = new MockTradeService();
Assert.Equal(3, mockTradeService.CreateAsyncCallCount);
Assert.True(mockTradeService.VerifyCreateAsync(expectedTrade));

// Yeni kullanım
var mockTradeService = CreateMockTradeService();
var service = new SomeService(mockTradeService.Object);

// Özel setup örneği
mockTradeService.Setup(x => x.CreateAsync(It.IsAny<Trade>()))
    .ReturnsAsync((Trade trade) => {
        trade.Id = 123;
        return trade;
    });

// Verify örnekleri
mockTradeService.Verify(x => x.CreateAsync(It.IsAny<Trade>()), Times.Exactly(3));
mockTradeService.Verify(x => x.CreateAsync(It.Is<Trade>(t =>
    t.UserId == expectedUserId &&
    t.CoinId == expectedCoinId &&
    t.CoinAmount == expectedAmount &&
    t.Type == TradeType.ReferralReward
)), Times.Once);
mockTradeService.Verify(x => x.CreateAsync(It.IsAny<Trade>()), Times.Never);
```

**Avantajları**:
- Daha esnek mock konfigürasyonu
- Setup metotları ile davranış belirleme
- Verify metotları ile çağrı doğrulama
- Interface-based dependency injection
- Moq'un güçlü It.Is() ve It.IsAny() matchers
- Times.Once, Times.Never, Times.Exactly() gibi esnek doğrulama seçenekleri

**Önemli Değişiklikler**:
- `CreateAsyncCallCount` → `mockTradeService.Verify(x => x.CreateAsync(It.IsAny<Trade>()), Times.Exactly(count))`
- `VerifyCreateAsync(expectedTrade)` → `mockTradeService.Verify(x => x.CreateAsync(It.Is<Trade>(t => ...)), Times.Once)`
- `CreateAsyncCalls` listesi artık kullanılmıyor, bunun yerine Verify ile kontrol ediliyor

## Gelecek Geçişler

### Sıradaki Mock Sınıfları:
1. `MockWalletService` → Moq Framework
2. `MockWalletServiceWithFailure` → Moq Framework

## Geçiş Süreci

Her mock sınıfı için aşağıdaki adımlar takip edilecek:

1. **TestBase'de yeni metot oluşturma**: Moq kullanarak mock oluşturan metot
2. **Obsolete işaretleme**: Eski mock sınıfını obsolete olarak işaretleme
3. **Test dosyalarını güncelleme**: Tüm kullanımları yeni metoda geçirme
4. **Geriye dönük uyumluluk**: Legacy metot ile eski kullanımları destekleme
5. **Doğrulama**: Testlerin çalıştığından emin olma

## Faydalar

- **Standartlaşma**: Tüm mocklar için aynı framework kullanımı
- **Esneklik**: Setup ve Verify metotları ile daha güçlü test senaryoları
- **Bakım kolaylığı**: Moq'un güncellemelerinden otomatik yararlanma
- **Öğrenme eğrisi**: Ekibin Moq framework'ünü daha iyi öğrenmesi
